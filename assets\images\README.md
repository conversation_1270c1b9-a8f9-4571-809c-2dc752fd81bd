# Required Images for Fusion Infotek Website

Please add the following images to this directory for the website to display properly:

## Main Website Images

### Homepage and About Section
- **home.png** - Hero section image (recommended size: 800x600px)
- **aboutus.png** - About section image (recommended size: 600x400px)

### Team Photos
- **shrawan.jpg** - Photo of <PERSON><PERSON><PERSON>, US Branch Head (recommended size: 400x400px, square format)
- **prasiddha.jpg** - Photo of Prasiddha Regmi, Nepal Branch Head (recommended size: 400x400px, square format)

## Blog Post Images

### Featured Images for Blog Posts
- **blog-web-development.jpg** - Featured image for "The Future of Web Development" post (recommended size: 1200x600px)
- **blog-mobile-security.jpg** - Featured image for "Mobile App Security Best Practices" post (recommended size: 1200x600px)
- **blog-digital-marketing.jpg** - Featured image for "Digital Marketing Strategies for 2025" post (recommended size: 1200x600px)

## Image Guidelines

### Format Recommendations
- **Photos**: Use JPG format for photographs
- **Graphics/Logos**: Use PNG format for graphics with transparency
- **Web Optimization**: Compress images for web use (under 500KB each)

### Size Guidelines
- **Hero Images**: 1200x800px or larger
- **Team Photos**: 400x400px (square format)
- **Blog Featured Images**: 1200x600px (2:1 aspect ratio)
- **About Images**: 600x400px or similar

### Quality Standards
- High resolution but web-optimized
- Professional appearance
- Consistent style and branding
- Good lighting and composition

## Alternative Options

If you don't have the specific images ready, you can:

1. **Use Placeholder Services**: 
   - Replace image sources with placeholder URLs like `https://via.placeholder.com/800x600`
   - Example: `<img src="https://via.placeholder.com/800x600/2563eb/ffffff?text=Home+Image" alt="Home">`

2. **Use Stock Photos**:
   - Professional stock photos from sites like Unsplash, Pexels, or Shutterstock
   - Ensure proper licensing for commercial use

3. **Create Simple Graphics**:
   - Use tools like Canva or Figma to create branded graphics
   - Maintain consistent color scheme (blue, white, black)

## Current Status

The website is fully functional and will display properly once these images are added to the `assets/images/` directory. The layout and styling are already optimized for the recommended image sizes.

## Notes

- All image paths in the HTML files are already configured
- Images will be automatically responsive and optimized for different screen sizes
- The website will work without images but will show broken image icons until images are added
