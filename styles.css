/* Custom CSS for Fusion Infotek Website */

/* Root Variables */
:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #3b82f6;
    --dark-color: #1f2937;
    --light-color: #f8fafc;
    --white-color: #ffffff;
    --text-dark: #374151;
    --text-light: #6b7280;
    --border-color: #e5e7eb;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Bootstrap Overrides */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transition: var(--transition);
}

.btn-primary:hover {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-2px);
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

/* Navigation Styles */
.navbar {
    padding: 1rem 0;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-logo {
    height: 40px;
    width: auto;
    transition: var(--transition);
}

.navbar-logo:hover {
    transform: scale(1.05);
}

.nav-link {
    font-weight: 500;
    margin: 0 0.5rem;
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--accent-color) !important;
    transform: translateY(-1px);
}

/* Schedule Meeting Button */
.schedule-meeting-btn {
    background: linear-gradient(135deg, #ff6b6b, #ffa500, #ff6b6b);
    background-size: 200% 200%;
    border: none;
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    animation: gradientShift 3s ease infinite;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.schedule-meeting-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    color: white;
}

.schedule-meeting-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.schedule-meeting-btn:hover::before {
    left: 100%;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Schedule Meeting Modal Styles */
.modal-content {
    border-radius: 20px;
    border: none;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.modal-header {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border-radius: 20px 20px 0 0;
    border-bottom: none;
    padding: 1.5rem 2rem;
}

.modal-title {
    font-weight: 600;
    font-size: 1.3rem;
}

.btn-close {
    filter: brightness(0) invert(1);
    opacity: 0.8;
}

.btn-close:hover {
    opacity: 1;
}

.modal-body {
    padding: 2rem;
}

.meeting-step {
    animation: slideInUp 0.5s ease-out;
}

.step-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.office-selection-card {
    background: var(--white-color);
    border: 2px solid var(--border-color);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 1rem;
}

.office-selection-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.2);
}

.office-selection-card.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), rgba(59, 130, 246, 0.05));
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.2);
}

.office-flag-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.3rem;
    color: white;
    transition: var(--transition);
}

.office-selection-card:hover .office-flag-icon {
    transform: scale(1.1);
    animation: pulse 1s infinite;
}

.office-selection-card h6 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.office-selection-card p {
    color: var(--text-light);
    margin-bottom: 0.3rem;
    font-size: 0.9rem;
}

.office-selection-card strong {
    color: var(--primary-color);
}

.form-label {
    font-weight: 500;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.form-control {
    border: 2px solid var(--border-color);
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: var(--transition);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.meeting-summary {
    background: var(--light-color);
    border-radius: 15px;
    padding: 1.5rem;
}

.summary-card h6 {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-label {
    font-weight: 500;
    color: var(--text-dark);
}

.summary-value {
    color: var(--primary-color);
    font-weight: 600;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 1.5rem 2rem;
    border-radius: 0 0 20px 20px;
}

.modal-footer .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    transition: var(--transition);
}

.modal-footer .btn:hover {
    transform: translateY(-2px);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-icon {
    font-size: 15rem;
    color: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.hero-img {
    max-width: 100%;
    height: auto;
    border-radius: 20px;
    animation: float 6s ease-in-out infinite;
}

.hero-buttons .btn {
    margin: 0.5rem;
    padding: 0.75rem 2rem;
    font-weight: 600;
    border-radius: 50px;
    transition: var(--transition);
}

.hero-scroll {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
}

.scroll-down {
    color: white;
    font-size: 1.5rem;
    animation: bounce 2s infinite;
    text-decoration: none;
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes slideInLeft {
    0% {
        opacity: 0;
        transform: translateX(-50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    0% {
        opacity: 0;
        transform: translateX(50px);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    0% {
        opacity: 0;
        transform: translateY(50px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes rotateIn {
    0% {
        opacity: 0;
        transform: rotate(-180deg) scale(0.5);
    }
    100% {
        opacity: 1;
        transform: rotate(0deg) scale(1);
    }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes heartbeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.1); }
    28% { transform: scale(1); }
    42% { transform: scale(1.1); }
    70% { transform: scale(1); }
}

/* Animation utility classes */
.animate-in {
    animation: fadeInScale 0.6s ease-out;
}

.animate-slide-left {
    animation: slideInLeft 0.8s ease-out;
}

.animate-slide-right {
    animation: slideInRight 0.8s ease-out;
}

.animate-slide-up {
    animation: slideInUp 0.8s ease-out;
}

.hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* Section Styles */
.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-light);
    margin-bottom: 2rem;
}

/* Alternating Section Backgrounds */
.section-white-bg {
    background: var(--white-color) !important;
    position: relative;
}

.section-alt-bg {
    background: rgba(37, 99, 235, 0.2) !important;
    position: relative;
}

.section-alt-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.15) 0%, rgba(59, 130, 246, 0.25) 100%);
    pointer-events: none;
}

.section-white-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 1) 100%);
    pointer-events: none;
}

.section-white-bg > .container,
.section-alt-bg > .container {
    position: relative;
    z-index: 1;
}

/* Values Section */
.values-section {
    padding: 5rem 0;
}

.value-card {
    background: var(--white-color);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
}

.value-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: var(--shadow-lg);
    animation: pulse 0.6s ease-in-out;
}

.value-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: var(--transition);
    animation: rotateIn 0.8s ease-out;
}

.value-card:hover .value-icon {
    transform: rotateY(360deg);
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
}

/* About Section */
.about-content {
    padding: 2rem 0;
}

.approach-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.about-icon {
    font-size: 12rem;
    color: var(--primary-color);
    opacity: 0.1;
}

.about-img {
    max-width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
}

.about-img:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Achievements Section */
.achievements-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 0.5rem;
}

.achievements-subtitle {
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 2rem;
}

.achievement-card {
    background: var(--white-color);
    padding: 3rem 2rem;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    margin-bottom: 2rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.achievement-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
}

.achievement-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(37, 99, 235, 0.2);
}

.achievement-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2rem;
    color: white;
    animation: pulse 2s infinite;
}

.achievement-number {
    font-size: 3.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
    line-height: 1;
}

.achievement-number .counter {
    display: inline-block;
}

.achievement-number .percentage,
.achievement-number .plus {
    color: var(--accent-color);
    font-size: 2.5rem;
}

.achievement-label {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1rem;
}

.achievement-desc {
    color: var(--text-light);
    font-size: 1rem;
    line-height: 1.5;
}

/* Service Cards */
.service-card {
    background: var(--white-color);
    padding: 2.5rem 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    text-align: center;
    height: 100%;
    border: 2px solid transparent;
}

.service-card:hover {
    transform: translateY(-15px) scale(1.03);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
    animation: pulse 0.6s ease-in-out;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    transition: var(--transition);
    animation: slideInUp 0.6s ease-out;
}

.service-card:hover .service-icon {
    transform: scale(1.1) rotate(5deg);
    background: linear-gradient(135deg, var(--accent-color), #ff6b6b);
}

/* Portfolio Cards */
.portfolio-card {
    background: var(--white-color);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    text-align: center;
    height: 100%;
}

.portfolio-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.portfolio-icon {
    width: 70px;
    height: 70px;
    background: var(--light-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.8rem;
    color: var(--primary-color);
}

/* Blog Cards */
.blog-card {
    background: var(--white-color);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.blog-date {
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    display: inline-block;
    margin-bottom: 1rem;
}

/* Mobile Blog Optimization */
.mobile-blog-more {
    background: var(--white-color);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    border: 2px solid var(--border-color);
}

.mobile-blog-more h6 {
    color: var(--primary-color);
    font-weight: 600;
    font-size: 1.1rem;
}

.mobile-blog-links {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.mobile-blog-link {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: var(--light-color);
    border-radius: 10px;
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.mobile-blog-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateX(5px);
    box-shadow: 0 3px 10px rgba(37, 99, 235, 0.3);
}

.mobile-blog-link i {
    color: var(--primary-color);
    transition: var(--transition);
}

.mobile-blog-link:hover i {
    color: white;
}

/* Location Flag Images */
.location-flag {
    width: 20px;
    height: 15px;
    object-fit: cover;
    border-radius: 3px;
    margin-right: 8px;
    vertical-align: middle;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.office-flag-image {
    width: 30px;
    height: 22px;
    object-fit: cover;
    border-radius: 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: var(--transition);
}

.office-flag-icon .office-flag-image {
    width: 40px;
    height: 30px;
    border-radius: 8px;
}

.office-flag-image:hover {
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

/* Team Cards */
.team-card {
    background: var(--white-color);
    padding: 2.5rem 2rem;
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    height: 100%;
}

.team-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-lg);
}

.team-avatar {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 3rem;
    color: white;
    overflow: hidden;
    position: relative;
}

.team-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    transition: var(--transition);
}

.team-photo:hover {
    transform: scale(1.1);
}

/* Portfolio Clickable Elements */
.portfolio-clickable {
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.portfolio-clickable:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
}

.team-avatar.portfolio-clickable {
    position: relative;
    overflow: hidden;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(37, 99, 235, 0.9);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition);
    border-radius: 50%;
}

.team-avatar.portfolio-clickable:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-overlay i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.portfolio-overlay span {
    font-size: 0.9rem;
    font-weight: 600;
}

.portfolio-link-btn {
    margin-top: 1rem;
}

.portfolio-link-btn .btn {
    border-radius: 20px;
    font-weight: 500;
    transition: var(--transition);
}

.portfolio-link-btn .btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(37, 99, 235, 0.3);
}

.team-role {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.team-qualification {
    color: var(--accent-color);
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
    font-style: italic;
}

.team-location {
    color: var(--text-light);
    margin-bottom: 1.5rem;
}

.team-social .social-link {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: var(--light-color);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    margin: 0 0.5rem;
    color: var(--primary-color);
    transition: var(--transition);
    text-decoration: none;
}

.team-social .social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* Contact Form */
.contact-form {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-form-white {
    background: var(--white-color);
    padding: 3rem;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
}

.form-floating > .form-control {
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 10px;
}

.contact-form-white .form-control {
    background: var(--light-color);
    border: 2px solid transparent;
    border-radius: 12px;
    padding: 1rem;
    font-size: 1rem;
    transition: var(--transition);
}

.contact-form-white .form-control:focus {
    background: var(--white-color);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(37, 99, 235, 0.25);
}

.contact-submit-btn {
    padding: 1rem 3rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border: none;
    position: relative;
    overflow: hidden;
    transition: var(--transition);
}

.contact-submit-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(37, 99, 235, 0.4);
}

/* Enhanced Contact Form Styling */
.contact-header {
    position: relative;
    margin-bottom: 3rem;
}

.contact-logo {
    display: flex;
    justify-content: center;
    align-items: center;
}

.contact-logo-img {
    height: 60px;
    width: auto;
    transition: var(--transition);
    animation: float 3s ease-in-out infinite;
}

.contact-logo-img:hover {
    transform: scale(1.05);
}

.contact-header-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
    animation: bounce 2s infinite;
}

.contact-divider {
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    margin: 1rem auto;
    border-radius: 2px;
}

.contact-form-container {
    background: var(--white-color);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.contact-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color), #ff6b6b);
}

.enhanced-contact-form {
    position: relative;
    z-index: 2;
}

.form-group-enhanced {
    position: relative;
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9));
    border-radius: 15px;
    padding: 0.4rem;
    box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
    transition: var(--transition);
}

.form-group-enhanced:hover {
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.12);
    transform: translateY(-1px);
}

.form-group-enhanced.focused {
    box-shadow: 0 8px 30px rgba(37, 99, 235, 0.2);
    background: linear-gradient(135deg, rgba(255, 255, 255, 1), rgba(248, 250, 252, 1));
}

.input-icon {
    position: absolute;
    top: 50%;
    left: 1rem;
    transform: translateY(-50%);
    color: var(--primary-color);
    font-size: 1.1rem;
    z-index: 3;
    transition: var(--transition);
}

.form-control-enhanced {
    width: 100%;
    padding: 1.5rem 1rem 0.5rem 3rem;
    border: 2px solid var(--border-color);
    border-radius: 15px;
    font-size: 1rem;
    background: rgba(248, 250, 252, 0.8);
    backdrop-filter: blur(10px);
    transition: var(--transition);
    position: relative;
    z-index: 2;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.form-control-enhanced::placeholder {
    color: transparent;
}

.form-control-enhanced:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 4px 20px rgba(37, 99, 235, 0.15);
    transform: translateY(-2px);
}

.form-control-enhanced:focus + .form-label-enhanced,
.form-control-enhanced:not(:placeholder-shown) + .form-label-enhanced {
    color: var(--primary-color);
    transform: translateY(-0.5rem) scale(0.85);
    font-weight: 600;
}

.form-label-enhanced {
    position: absolute;
    top: 1.2rem;
    left: 3rem;
    color: var(--text-light);
    font-size: 1rem;
    font-weight: 500;
    transition: var(--transition);
    pointer-events: none;
    z-index: 3;
    background: transparent;
}

.input-border {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    transition: width 0.3s ease;
    border-radius: 0 0 15px 15px;
}

.form-control-enhanced:focus ~ .input-border {
    width: 100%;
}

.btn-enhanced-submit {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
    border: none;
    padding: 1rem 3rem;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.btn-enhanced-submit:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(37, 99, 235, 0.4);
}

.btn-enhanced-submit:active {
    transform: translateY(-1px);
}

.btn-enhanced-submit::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.btn-enhanced-submit:hover::before {
    left: 100%;
}

/* Office Selection Styling */
.office-selection-container {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 250, 252, 0.9));
    border-radius: 15px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.office-selection-title {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1.5rem;
    font-size: 1.2rem;
}

.office-buttons-row {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.office-option {
    flex: 1;
    max-width: 300px;
    min-width: 250px;
}

.office-card {
    background: var(--white-color);
    border: 2px solid var(--border-color);
    border-radius: 15px;
    padding: 1.5rem 1rem;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.office-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.office-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(37, 99, 235, 0.2);
}

.office-card:hover::before {
    transform: scaleX(1);
}

.office-card.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), rgba(59, 130, 246, 0.05));
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(37, 99, 235, 0.2);
}

.office-card.selected::before {
    transform: scaleX(1);
}

.office-flag {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 0.75rem;
    font-size: 1.3rem;
    color: white;
    transition: var(--transition);
}

.office-card:hover .office-flag {
    transform: scale(1.1);
    animation: pulse 1s infinite;
}

.office-card h6 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.office-card p {
    color: var(--text-light);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.office-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: slideInUp 0.5s ease-out;
}

.action-btn {
    padding: 0.6rem 1.2rem;
    border: none;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.85rem;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    min-width: 110px;
    justify-content: center;
}

.call-btn {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

.call-btn:hover {
    background: linear-gradient(135deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(16, 185, 129, 0.4);
}

.email-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    color: white;
}

.email-btn:hover {
    background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(37, 99, 235, 0.4);
}

.form-submission-status {
    margin-top: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1), rgba(5, 150, 105, 0.1));
    border-radius: 15px;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-content {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #059669;
    font-weight: 600;
}

/* Contact Info Cards */
.contact-info-card {
    background: var(--white-color);
    padding: 2rem;
    border-radius: 20px;
    box-shadow: var(--shadow);
    text-align: center;
    transition: var(--transition);
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
}

.contact-info-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.contact-info-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 1.5rem;
    color: white;
}

.contact-info-card h5 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.contact-info-card p {
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    font-size: 0.95rem;
}

.contact-info-card a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

.contact-info-card a:hover {
    color: var(--accent-color);
    text-decoration: underline;
}

.contact-info h5 {
    margin-bottom: 1rem;
    font-weight: 600;
}

.contact-info p {
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

/* Footer */
footer {
    background: var(--dark-color) !important;
}

.footer-logo {
    display: flex;
    align-items: center;
}

.footer-logo-img {
    height: 35px;
    width: auto;
    transition: var(--transition);
}

.footer-logo-img:hover {
    transform: scale(1.05);
}

.footer-section h5 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.location-item {
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.location-item:last-child {
    border-bottom: none;
}

.social-links .social-link {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    color: white;
    transition: var(--transition);
    text-decoration: none;
}

.social-links .social-link:hover {
    background: var(--accent-color);
    transform: translateY(-2px);
}

.footer-link {
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
}

.footer-link:hover {
    color: var(--primary-color);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: none;
    z-index: 1000;
    border: none;
    box-shadow: var(--shadow-lg);
    transition: var(--transition);
}

.back-to-top:hover {
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 2rem 0;
    }
    
    .hero-icon {
        font-size: 8rem;
        margin-top: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .value-card,
    .service-card,
    .portfolio-card,
    .blog-card,
    .team-card {
        margin-bottom: 2rem;
    }
    
    .contact-form {
        margin: 1rem 0;
    }

    .contact-form-container {
        padding: 1.5rem 1rem;
        margin: 1rem 0;
    }

    .form-group-enhanced {
        margin-bottom: 1.5rem;
    }

    .btn-enhanced-submit {
        width: 100%;
        padding: 1rem 2rem;
    }

    .contact-info-card {
        margin-bottom: 1.5rem;
    }

    .office-buttons-row {
        flex-direction: column;
        gap: 1.5rem;
    }

    .office-option {
        max-width: 100%;
    }

    .office-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .action-btn {
        width: 100%;
        min-width: auto;
    }

    .office-selection-container {
        padding: 1rem;
        margin-top: 1rem;
    }

    .office-card {
        padding: 1.2rem 0.8rem;
    }

    .office-flag {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
        margin-bottom: 0.5rem;
    }
}

@media (max-width: 576px) {
    .hero-content h1 {
        font-size: 2rem;
    }
    
    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin: 0.5rem 0;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }

    .navbar-logo {
        height: 35px;
    }

    .footer-logo-img {
        height: 30px;
    }

    .contact-logo-img {
        height: 50px;
    }

    /* Mobile Blog Optimization */
    .blog-post-hidden-mobile {
        display: none !important;
    }

    .mobile-blog-more {
        padding: 1.5rem;
    }

    .mobile-blog-link {
        padding: 0.8rem;
        font-size: 0.9rem;
    }

    /* Mobile Flag Adjustments */
    .location-flag {
        width: 18px;
        height: 13px;
        margin-right: 6px;
    }

    .office-flag-image {
        width: 25px;
        height: 18px;
    }

    .office-flag-icon .office-flag-image {
        width: 35px;
        height: 25px;
    }
}
