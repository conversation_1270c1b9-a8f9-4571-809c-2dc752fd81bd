# Fusion Infotek - Professional IT Company Website

A modern, mobile-responsive static website for Fusion Infotek, showcasing complete digital and software solutions.

## 🚀 Features

### Core Functionality
- **Fully Responsive Design** - Optimized for all devices (mobile, tablet, desktop)
- **Modern UI/UX** - Clean, professional design with smooth animations
- **Interactive Navigation** - Smooth scrolling with active section highlighting
- **Contact Form** - Functional contact form with validation
- **Meeting Scheduler** - Branch-specific meeting booking system
- **Performance Optimized** - Fast loading with optimized assets

### Sections Included
- **Home** - Hero section with company overview and core values
- **About** - Company information and approach
- **Services** - Six core services with detailed descriptions
- **Portfolio** - Project showcase in four categories
- **Blog** - Latest updates and insights
- **Team** - Leadership profiles for both branches
- **Contact** - Contact form and office information

## 🛠️ Technologies Used

- **HTML5** - Semantic markup structure
- **CSS3** - Custom styling with CSS Grid and Flexbox
- **JavaScript (ES6+)** - Interactive functionality
- **Bootstrap 5.3** - Responsive framework
- **Font Awesome 6.4** - Professional icons
- **Google Fonts** - Inter font family

## 📁 File Structure

```
fusion-infotek-website/
├── index.html          # Main HTML file
├── styles.css          # Custom CSS styles
├── script.js           # JavaScript functionality
├── README.md           # Project documentation
└── assets/             # Images and other assets (to be added)
```

## 🎨 Design Features

### Color Palette
- **Primary Blue**: #2563eb
- **Secondary Blue**: #1e40af
- **Accent Blue**: #3b82f6
- **Dark Gray**: #1f2937
- **Light Gray**: #f8fafc
- **White**: #ffffff

### Typography
- **Font Family**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700

### Animations
- Smooth hover effects on cards and buttons
- Floating animation for hero icon
- Scroll-triggered animations for sections
- Typing effect for hero title
- Bounce animation for scroll indicator

## 📱 Mobile Responsiveness

The website is fully responsive with breakpoints at:
- **Desktop**: 1200px and above
- **Tablet**: 768px - 1199px
- **Mobile**: Below 768px

### Mobile Features
- Collapsible navigation menu
- Touch-friendly buttons and links
- Optimized font sizes and spacing
- Stacked layout for better readability

## 🔧 Interactive Features

### Navigation
- Fixed navigation bar with scroll effects
- Smooth scrolling to sections
- Active section highlighting
- Mobile hamburger menu

### Contact Form
- Real-time form validation
- Success/error notifications
- Responsive form layout
- Email format validation

### Meeting Scheduler
- Branch-specific contact information
- Modal popup with contact details
- Direct email links for meeting requests
- Time zone information

### Additional Features
- Back to top button
- Scroll animations
- Loading states for form submission
- Cross-browser compatibility

## 🏢 Company Information

### Fusion Infotek Inc.
- **Main Office**: Albany, NY
- **Registered Agent**: ZenBusiness Inc.
- **Address**: 41 State Street Suite 112, Albany, NY 12207
- **Authorized Shares**: 10,000 @ $0.10/share
- **Status**: Perpetual Corporation

### Branch Offices

#### United States Branch
- **Head**: Shrawan Sharma
- **Location**: Albany, NY
- **Email**: <EMAIL>
- **Phone**: ************

#### Nepal Branch
- **Head**: Prasiddha Regmi
- **Location**: Banasthali, Kathmandu
- **Email**: <EMAIL>
- **Phone**: **********

## 🚀 Getting Started

1. **Clone or Download** the project files
2. **Open** `index.html` in a web browser
3. **Customize** content, colors, and images as needed
4. **Deploy** to your preferred hosting platform

### Local Development
```bash
# No build process required - just open in browser
open index.html

# Or use a local server (recommended)
python -m http.server 8000
# Then visit http://localhost:8000
```

## 🔧 Customization

### Changing Colors
Edit the CSS custom properties in `styles.css`:
```css
:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    /* Add your custom colors */
}
```

### Adding Images
1. Create an `assets/images/` folder
2. Add your images
3. Update image sources in HTML
4. Optimize images for web (WebP format recommended)

### Modifying Content
- Edit text content directly in `index.html`
- Update company information in the footer
- Modify service descriptions and team profiles
- Add or remove portfolio items

## 📈 Performance Optimization

### Implemented Optimizations
- Minified CSS and JavaScript (production ready)
- Optimized images with lazy loading
- Efficient CSS animations
- Reduced HTTP requests
- Mobile-first responsive design

### Additional Recommendations
- Compress images (use WebP format)
- Enable GZIP compression on server
- Use CDN for static assets
- Implement caching headers
- Consider Progressive Web App features

## 🌐 Browser Support

- **Chrome** 90+
- **Firefox** 88+
- **Safari** 14+
- **Edge** 90+
- **Mobile browsers** (iOS Safari, Chrome Mobile)

## 📞 Support

For technical support or customization requests:
- **Email**: <EMAIL>
- **Phone**: ************ (US) | ********** (Nepal)

## 📄 License

This website template is created for Fusion Infotek Inc. All rights reserved.

---

**Built with ❤️ for Fusion Infotek - Complete Digital & Software Solutions**
