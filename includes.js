// Include system for HTML components
function includeHTML() {
    const elements = document.querySelectorAll('[data-include]');
    
    elements.forEach(async (element) => {
        const file = element.getAttribute('data-include');
        if (file) {
            try {
                const response = await fetch(file);
                if (response.ok) {
                    const html = await response.text();
                    element.innerHTML = html;
                    element.removeAttribute('data-include');
                } else {
                    element.innerHTML = `<p>Error loading ${file}</p>`;
                }
            } catch (error) {
                element.innerHTML = `<p>Error loading ${file}</p>`;
                console.error('Include error:', error);
            }
        }
    });
}

// Load includes when DOM is ready
document.addEventListener('DOMContentLoaded', includeHTML);
