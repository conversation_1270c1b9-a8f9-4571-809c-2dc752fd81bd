/* Blog Post Styles */

.blog-post-main {
    padding-top: 120px;
    padding-bottom: 60px;
    background: var(--light-color);
}

.blog-post {
    background: var(--white-color);
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    overflow: hidden;
    margin-bottom: 2rem;
}

.blog-post-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--border-color);
}

.blog-breadcrumb {
    margin-bottom: 1rem;
}

.breadcrumb {
    background: none;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: var(--primary-color);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    text-decoration: underline;
}

.blog-post-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.blog-post-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.author-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid var(--primary-color);
}

.author-details {
    display: flex;
    flex-direction: column;
}

.author-name {
    font-weight: 600;
    color: var(--dark-color);
}

.author-qualification {
    font-size: 0.85rem;
    color: var(--primary-color);
    font-style: italic;
    font-weight: 500;
}

.post-date {
    font-size: 0.9rem;
    color: var(--text-light);
}

.post-tags {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.tag {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.blog-post-content {
    padding: 0;
}

.featured-image {
    width: 100%;
    height: 400px;
    overflow: hidden;
}

.featured-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.featured-image img:hover {
    transform: scale(1.05);
}

.content-body {
    padding: 2rem;
    line-height: 1.8;
}

.content-body .lead {
    font-size: 1.2rem;
    font-weight: 400;
    color: var(--text-dark);
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--light-color);
    border-left: 4px solid var(--primary-color);
    border-radius: 0 10px 10px 0;
}

.content-body h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 2rem 0 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.content-body h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 1.5rem 0 1rem;
}

.content-body p {
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.content-body ul {
    margin-bottom: 1.5rem;
    padding-left: 2rem;
}

.content-body li {
    margin-bottom: 0.5rem;
    color: var(--text-dark);
}

.content-body strong {
    color: var(--primary-color);
    font-weight: 600;
}

.blog-post-footer {
    padding: 2rem;
    border-top: 1px solid var(--border-color);
    background: var(--light-color);
}

.share-buttons h5 {
    margin-bottom: 1rem;
    color: var(--dark-color);
}

.share-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin-right: 1rem;
    text-decoration: none;
    color: white;
    transition: var(--transition);
    font-size: 1.2rem;
}

.share-btn.facebook {
    background: #1877f2;
}

.share-btn.linkedin {
    background: #0077b5;
}

.share-btn.twitter {
    background: #1da1f2;
}

.share-btn.email {
    background: var(--dark-color);
}

.share-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    color: white;
}

/* Sidebar Styles */
.blog-sidebar {
    padding-left: 2rem;
}

.sidebar-widget {
    background: var(--white-color);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow);
    transition: var(--transition);
}

.sidebar-widget:hover {
    box-shadow: var(--shadow-lg);
}

.sidebar-widget h4 {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.related-post {
    padding: 1.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.related-post:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.related-post-link {
    text-decoration: none;
    color: inherit;
    display: block;
    transition: var(--transition);
}

.related-post-link:hover {
    color: var(--primary-color);
    transform: translateX(5px);
}

.related-post h6 {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.related-post-excerpt {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 0.5rem;
    line-height: 1.5;
}

.read-more {
    font-size: 0.9rem;
    color: var(--primary-color);
    font-weight: 500;
}

.expert-contact {
    text-align: center;
}

.expert-contact p {
    margin-bottom: 1.5rem;
    color: var(--text-dark);
}

.expert-contact .btn {
    padding: 0.75rem 2rem;
    border-radius: 25px;
    font-weight: 600;
}

.sidebar-widget .social-links {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.sidebar-widget .social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.sidebar-widget .social-link:hover {
    background: var(--accent-color);
    transform: translateY(-3px);
    color: white;
}

/* Responsive Design */
@media (max-width: 992px) {
    .blog-sidebar {
        padding-left: 0;
        margin-top: 2rem;
    }
    
    .blog-post-title {
        font-size: 2rem;
    }
    
    .blog-post-meta {
        flex-direction: column;
        align-items: flex-start;
    }
}

@media (max-width: 768px) {
    .blog-post-main {
        padding-top: 100px;
    }
    
    .blog-post-header {
        padding: 1.5rem 1.5rem 1rem;
    }
    
    .content-body {
        padding: 1.5rem;
    }
    
    .blog-post-footer {
        padding: 1.5rem;
    }
    
    .blog-post-title {
        font-size: 1.8rem;
    }
    
    .featured-image {
        height: 250px;
    }
    
    .sidebar-widget {
        padding: 1.5rem;
    }
}
