// Fusion Infotek Website JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all functionality
    initNavigation();
    initScrollEffects();
    initContactForm();
    initMeetingScheduler();
    initAnimations();
    initBackToTop();
    initScheduleMeetingModal();
    initMobileBlogSection();
    initTeamPortfolios();
});

// Navigation functionality
function initNavigation() {
    const navbar = document.querySelector('.navbar');
    const navLinks = document.querySelectorAll('.nav-link');
    
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.style.background = 'rgba(37, 99, 235, 0.95)';
            navbar.style.backdropFilter = 'blur(10px)';
        } else {
            navbar.style.background = 'rgba(37, 99, 235, 1)';
            navbar.style.backdropFilter = 'none';
        }
    });
    
    // Smooth scrolling for navigation links
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80;
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
                
                // Close mobile menu if open
                const navbarCollapse = document.querySelector('.navbar-collapse');
                if (navbarCollapse.classList.contains('show')) {
                    const bsCollapse = new bootstrap.Collapse(navbarCollapse);
                    bsCollapse.hide();
                }
            }
        });
    });
    
    // Active navigation highlighting
    window.addEventListener('scroll', updateActiveNavigation);
}

// Update active navigation based on scroll position
function updateActiveNavigation() {
    const sections = document.querySelectorAll('section[id]');
    const navLinks = document.querySelectorAll('.nav-link');
    
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop - 100;
        const sectionHeight = section.offsetHeight;
        
        if (window.scrollY >= sectionTop && window.scrollY < sectionTop + sectionHeight) {
            current = section.getAttribute('id');
        }
    });
    
    navLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
        }
    });
}

// Scroll effects and animations
function initScrollEffects() {
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach((entry, index) => {
            if (entry.isIntersecting) {
                // Add staggered animation delay
                setTimeout(() => {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0) scale(1)';
                    entry.target.classList.add('animate-in');
                }, index * 100);
            }
        });
    }, observerOptions);

    // Observe elements for animation with different effects
    const animateElements = document.querySelectorAll('.value-card, .service-card, .portfolio-card, .blog-card, .team-card');
    animateElements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(50px) scale(0.9)';
        el.style.transition = 'opacity 0.8s ease, transform 0.8s ease';
        observer.observe(el);
    });

    // Add parallax effect to hero section
    window.addEventListener('scroll', () => {
        const scrolled = window.pageYOffset;
        const heroImage = document.querySelector('.hero-img');
        const aboutImage = document.querySelector('.about-img');

        if (heroImage) {
            heroImage.style.transform = `translateY(${scrolled * 0.1}px)`;
        }

        if (aboutImage) {
            aboutImage.style.transform = `translateY(${scrolled * 0.05}px)`;
        }
    });
}

// Contact form functionality
function initContactForm() {
    const contactForm = document.getElementById('contactForm');

    if (contactForm) {
        // Enhanced form field interactions
        const formControls = contactForm.querySelectorAll('.form-control-enhanced');
        formControls.forEach(control => {
            control.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
            });

            control.addEventListener('blur', function() {
                if (!this.value) {
                    this.parentElement.classList.remove('focused');
                }
            });

            control.addEventListener('input', function() {
                if (this.value) {
                    this.parentElement.classList.add('has-value');
                } else {
                    this.parentElement.classList.remove('has-value');
                }
            });
        });

        // Office selection functionality
        const officeCards = contactForm.querySelectorAll('.office-card');
        const officeOptions = contactForm.querySelectorAll('.office-option');

        officeCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                officeCards.forEach(c => c.classList.remove('selected'));

                // Add selected class to clicked card
                this.classList.add('selected');

                // Hide all office actions first
                const allActions = contactForm.querySelectorAll('.office-actions');
                allActions.forEach(action => action.style.display = 'none');

                // Show actions for selected office
                const actions = this.querySelector('.office-actions');
                actions.style.display = 'flex';
            });
        });

        // Handle email button clicks
        const emailButtons = contactForm.querySelectorAll('.email-btn');
        emailButtons.forEach(btn => {
            btn.addEventListener('click', function(e) {
                e.preventDefault();

                // Get form data
                const name = document.getElementById('name').value;
                const email = document.getElementById('email').value;
                const message = document.getElementById('message').value;

                // Basic validation
                if (!name || !email || !message) {
                    showNotification('Please fill in all fields', 'error');
                    return;
                }

                if (!isValidEmail(email)) {
                    showNotification('Please enter a valid email address', 'error');
                    return;
                }

                // Get recipient email
                const recipientEmail = this.getAttribute('data-email');

                // Show loading status
                const statusDiv = contactForm.querySelector('.form-submission-status');
                statusDiv.style.display = 'block';

                // Simulate sending email
                setTimeout(() => {
                    // Create Gmail compose URL with form data
                    const subject = encodeURIComponent(`New Contact Form Submission from ${name}`);
                    const body = encodeURIComponent(`Dear Team,

I hope this message finds you well. I am reaching out through your website contact form.

Contact Details:
Name: ${name}
Email: ${email}

Message:
${message}

I look forward to hearing from you soon.

Best regards,
${name}`);

                    // Create Gmail compose URL
                    const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=${recipientEmail}&su=${subject}&body=${body}`;

                    // Open Gmail in new tab
                    window.open(gmailUrl, '_blank');

                    // Show success message
                    showNotification('Gmail opened in new tab! Your message is ready to send.', 'success');

                    // Reset form
                    contactForm.reset();

                    // Reset form states
                    formControls.forEach(control => {
                        control.parentElement.classList.remove('focused', 'has-value');
                    });

                    // Hide office actions and status
                    officeCards.forEach(c => c.classList.remove('selected'));
                    const allActions = contactForm.querySelectorAll('.office-actions');
                    allActions.forEach(action => action.style.display = 'none');
                    statusDiv.style.display = 'none';
                }, 1500);
            });
        });
    }
}

// Meeting scheduler functionality
function initMeetingScheduler() {
    const meetingDropdown = document.querySelectorAll('[data-branch]');
    const meetingModal = new bootstrap.Modal(document.getElementById('meetingModal'));
    
    meetingDropdown.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const branch = this.getAttribute('data-branch');
            showMeetingModal(branch);
        });
    });
    
    function showMeetingModal(branch) {
        const modalContent = document.getElementById('meetingContent');
        let content = '';
        
        if (branch === 'us') {
            content = `
                <div class="text-center mb-4">
                    <i class="fas fa-flag-usa fa-3x text-primary mb-3"></i>
                    <h4>Schedule Meeting - United States Office</h4>
                    <p class="text-muted">Albany, NY</p>
                </div>
                <div class="contact-details">
                    <p><strong>Contact Person:</strong> Shrawan Sharma</p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p><strong>Phone:</strong> <a href="tel:************">************</a></p>
                    <p><strong>Time Zone:</strong> EST (Eastern Standard Time)</p>
                </div>
                <div class="mt-4">
                    <a href="mailto:<EMAIL>?subject=Meeting Request&body=Hi, I would like to schedule a meeting to discuss my project requirements." 
                       class="btn btn-primary btn-lg w-100">
                        <i class="fas fa-envelope me-2"></i>Send Meeting Request
                    </a>
                </div>
            `;
        } else if (branch === 'nepal') {
            content = `
                <div class="text-center mb-4">
                    <i class="fas fa-mountain fa-3x text-primary mb-3"></i>
                    <h4>Schedule Meeting - Nepal Office</h4>
                    <p class="text-muted">Banasthali, Kathmandu</p>
                </div>
                <div class="contact-details">
                    <p><strong>Contact Person:</strong> Prasiddha Regmi</p>
                    <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p><strong>Phone:</strong> <a href="tel:9863144095">9863144095</a></p>
                    <p><strong>Time Zone:</strong> NPT (Nepal Time)</p>
                </div>
                <div class="mt-4">
                    <a href="mailto:<EMAIL>?subject=Meeting Request&body=Hi, I would like to schedule a meeting to discuss my project requirements." 
                       class="btn btn-primary btn-lg w-100">
                        <i class="fas fa-envelope me-2"></i>Send Meeting Request
                    </a>
                </div>
            `;
        }
        
        modalContent.innerHTML = content;
        meetingModal.show();
    }
}

// Initialize animations
function initAnimations() {
    // Counter animation for achievements section
    function animateCounter(element, target, duration = 2000) {
        let start = 1;
        const increment = (target - 1) / (duration / 16);

        const timer = setInterval(() => {
            start += increment;
            element.textContent = Math.floor(start);

            if (start >= target) {
                element.textContent = target;
                clearInterval(timer);
            }
        }, 16);
    }

    // Initialize counter animations when achievements section comes into view
    const achievementObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const counters = entry.target.querySelectorAll('.counter');
                counters.forEach(counter => {
                    const target = parseInt(counter.getAttribute('data-target'));
                    animateCounter(counter, target, 2500);
                });
                achievementObserver.unobserve(entry.target);
            }
        });
    }, {
        threshold: 0.1,
        rootMargin: '0px 0px -20px 0px'
    });

    // Try multiple selectors to find the achievements section
    const achievementsSection = document.querySelector('.achievement-card') ||
                               document.querySelector('[data-target]') ||
                               document.querySelector('.counter');

    if (achievementsSection) {
        const sectionToObserve = achievementsSection.closest('section') ||
                               achievementsSection.closest('.row') ||
                               achievementsSection.parentElement;
        if (sectionToObserve) {
            achievementObserver.observe(sectionToObserve);
        }
    }

    // Fallback: trigger animation on scroll for mobile
    let animationTriggered = false;
    window.addEventListener('scroll', () => {
        if (!animationTriggered) {
            const counters = document.querySelectorAll('.counter');
            if (counters.length > 0) {
                const firstCounter = counters[0];
                const rect = firstCounter.getBoundingClientRect();
                const windowHeight = window.innerHeight;

                if (rect.top < windowHeight * 0.8) {
                    counters.forEach(counter => {
                        const target = parseInt(counter.getAttribute('data-target'));
                        animateCounter(counter, target, 2500);
                    });
                    animationTriggered = true;
                }
            }
        }
    });
    
    // Typing effect for hero title (optional)
    const heroTitle = document.querySelector('.hero-content h1');
    if (heroTitle) {
        const text = heroTitle.textContent;
        heroTitle.textContent = '';
        
        let i = 0;
        const typeWriter = () => {
            if (i < text.length) {
                heroTitle.textContent += text.charAt(i);
                i++;
                setTimeout(typeWriter, 50);
            }
        };
        
        // Start typing effect after a delay
        setTimeout(typeWriter, 1000);
    }
}

// Back to top button
function initBackToTop() {
    const backToTopBtn = document.getElementById('backToTop');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 300) {
            backToTopBtn.style.display = 'block';
        } else {
            backToTopBtn.style.display = 'none';
        }
    });
    
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Utility functions
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} notification`;
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.3s ease;
    `;
    
    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => notification.remove(), 300);
    }, 5000);
}

// Portfolio filter functionality (for future enhancement)
function initPortfolioFilter() {
    const filterButtons = document.querySelectorAll('.portfolio-filter');
    const portfolioItems = document.querySelectorAll('.portfolio-item');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');
            
            // Update active button
            filterButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');
            
            // Filter items
            portfolioItems.forEach(item => {
                if (filter === 'all' || item.classList.contains(filter)) {
                    item.style.display = 'block';
                    item.style.opacity = '1';
                } else {
                    item.style.opacity = '0';
                    setTimeout(() => {
                        item.style.display = 'none';
                    }, 300);
                }
            });
        });
    });
}

// Initialize tooltips and popovers (Bootstrap)
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

// Performance optimization
window.addEventListener('load', function() {
    // Lazy load images when they come into view
    const images = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
});

// Schedule Meeting Modal functionality
function initScheduleMeetingModal() {
    const modal = document.getElementById('scheduleMeetingModal');
    const form = document.getElementById('scheduleMeetingForm');
    const steps = document.querySelectorAll('.meeting-step');
    const nextBtn = document.getElementById('nextStep');
    const prevBtn = document.getElementById('prevStep');
    const sendBtn = document.getElementById('sendMeetingRequest');
    const officeCards = document.querySelectorAll('.office-selection-card');

    let currentStep = 1;
    let selectedOffice = null;
    let meetingData = {};

    // Office selection
    officeCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove selected class from all cards
            officeCards.forEach(c => c.classList.remove('selected'));

            // Add selected class to clicked card
            this.classList.add('selected');

            // Store selected office
            selectedOffice = this.getAttribute('data-office');

            // Enable next button
            nextBtn.disabled = false;
        });
    });

    // Next button functionality
    nextBtn.addEventListener('click', function() {
        if (currentStep === 1 && selectedOffice) {
            showStep(2);
        } else if (currentStep === 2 && validateStep2()) {
            collectMeetingData();
            showMeetingSummary();
            showStep(3);
        }
    });

    // Previous button functionality
    prevBtn.addEventListener('click', function() {
        if (currentStep === 2) {
            showStep(1);
        } else if (currentStep === 3) {
            showStep(2);
        }
    });

    // Send meeting request
    sendBtn.addEventListener('click', function() {
        sendMeetingRequest();
    });

    // Show specific step
    function showStep(step) {
        // Hide all steps
        steps.forEach(s => s.style.display = 'none');

        // Show current step
        document.getElementById(`step${step}`).style.display = 'block';

        // Update buttons
        currentStep = step;
        updateButtons();
    }

    // Update button visibility
    function updateButtons() {
        prevBtn.style.display = currentStep > 1 ? 'inline-block' : 'none';
        nextBtn.style.display = currentStep < 3 ? 'inline-block' : 'none';
        sendBtn.style.display = currentStep === 3 ? 'inline-block' : 'none';

        if (currentStep === 1) {
            nextBtn.disabled = !selectedOffice;
        } else {
            nextBtn.disabled = false;
        }
    }

    // Validate step 2 form
    function validateStep2() {
        const name = document.getElementById('meetingName').value;
        const email = document.getElementById('meetingEmail').value;
        const date = document.getElementById('meetingDate').value;
        const time = document.getElementById('meetingTime').value;
        const purpose = document.getElementById('meetingPurpose').value;

        if (!name || !email || !date || !time || !purpose) {
            showNotification('Please fill in all meeting details', 'error');
            return false;
        }

        if (!isValidEmail(email)) {
            showNotification('Please enter a valid email address', 'error');
            return false;
        }

        // Check if date is in the future
        const selectedDate = new Date(date);
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        if (selectedDate < today) {
            showNotification('Please select a future date', 'error');
            return false;
        }

        return true;
    }

    // Collect meeting data
    function collectMeetingData() {
        meetingData = {
            office: selectedOffice,
            name: document.getElementById('meetingName').value,
            email: document.getElementById('meetingEmail').value,
            date: document.getElementById('meetingDate').value,
            time: document.getElementById('meetingTime').value,
            purpose: document.getElementById('meetingPurpose').value
        };
    }

    // Show meeting summary
    function showMeetingSummary() {
        const summaryDiv = document.getElementById('meetingSummary');
        const officeInfo = selectedOffice === 'us' ?
            { name: 'United States Office', contact: 'Shrawan Sharma', email: '<EMAIL>' } :
            { name: 'Nepal Office', contact: 'Prasiddha Regmi', email: '<EMAIL>' };

        const formattedDate = new Date(meetingData.date).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        const formattedTime = new Date(`2000-01-01T${meetingData.time}`).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });

        summaryDiv.innerHTML = `
            <div class="summary-item">
                <span class="summary-label">Office:</span>
                <span class="summary-value">${officeInfo.name}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Contact Person:</span>
                <span class="summary-value">${officeInfo.contact}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Your Name:</span>
                <span class="summary-value">${meetingData.name}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Your Email:</span>
                <span class="summary-value">${meetingData.email}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Date:</span>
                <span class="summary-value">${formattedDate}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Time:</span>
                <span class="summary-value">${formattedTime}</span>
            </div>
            <div class="summary-item">
                <span class="summary-label">Purpose:</span>
                <span class="summary-value">${meetingData.purpose}</span>
            </div>
        `;
    }

    // Send meeting request via Gmail
    function sendMeetingRequest() {
        const officeInfo = selectedOffice === 'us' ?
            { name: 'United States Office', contact: 'Shrawan Sharma', email: '<EMAIL>' } :
            { name: 'Nepal Office', contact: 'Prasiddha Regmi', email: '<EMAIL>' };

        const formattedDate = new Date(meetingData.date).toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        const formattedTime = new Date(`2000-01-01T${meetingData.time}`).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });

        const subject = encodeURIComponent(`Meeting Request - ${meetingData.name} - ${formattedDate}`);
        const body = encodeURIComponent(`Dear ${officeInfo.contact},

I hope this email finds you well. I would like to schedule a meeting with you.

Meeting Details:
Name: ${meetingData.name}
Email: ${meetingData.email}
Preferred Date: ${formattedDate}
Preferred Time: ${formattedTime}
Office: ${officeInfo.name}

Meeting Purpose:
${meetingData.purpose}

Please let me know if this time works for you, or suggest an alternative time that would be more convenient.

Looking forward to hearing from you.

Best regards,
${meetingData.name}
${meetingData.email}`);

        // Create Gmail compose URL
        const gmailUrl = `https://mail.google.com/mail/?view=cm&fs=1&to=${officeInfo.email}&su=${subject}&body=${body}`;

        // Open Gmail in new tab
        window.open(gmailUrl, '_blank');

        // Show success message
        showNotification('Gmail opened! Your meeting request is ready to send.', 'success');

        // Close modal and reset form
        const modalInstance = bootstrap.Modal.getInstance(modal);
        modalInstance.hide();
        resetMeetingForm();
    }

    // Reset form when modal is closed
    modal.addEventListener('hidden.bs.modal', function() {
        resetMeetingForm();
    });

    // Reset meeting form
    function resetMeetingForm() {
        currentStep = 1;
        selectedOffice = null;
        meetingData = {};

        // Reset form
        form.reset();

        // Remove selected classes
        officeCards.forEach(c => c.classList.remove('selected'));

        // Show first step
        showStep(1);

        // Set minimum date to today
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('meetingDate').setAttribute('min', today);
    }

    // Initialize form
    resetMeetingForm();
}

// Mobile Blog Section functionality
function initMobileBlogSection() {
    const viewAllBlogsBtn = document.getElementById('viewAllBlogs');
    const hiddenBlogPosts = document.querySelectorAll('.blog-post-hidden-mobile');
    const mobileBlogMore = document.querySelector('.mobile-blog-more');

    if (viewAllBlogsBtn) {
        viewAllBlogsBtn.addEventListener('click', function() {
            // Show hidden blog posts
            hiddenBlogPosts.forEach(post => {
                post.classList.remove('blog-post-hidden-mobile');
                post.style.display = 'block';
                post.style.animation = 'slideInUp 0.5s ease-out';
            });

            // Hide the mobile blog more section
            if (mobileBlogMore) {
                mobileBlogMore.style.animation = 'fadeOut 0.3s ease-out';
                setTimeout(() => {
                    mobileBlogMore.style.display = 'none';
                }, 300);
            }

            // Show success notification
            showNotification('All blog posts are now visible!', 'success');
        });
    }
}

// Team Portfolio functionality
function initTeamPortfolios() {
    // Team member data
    window.teamData = {
        shrawan: {
            name: "Shrawan Sharma",
            role: "US Branch Head & Full Stack Developer",
            education: "MTech in Computer Application",
            location: "Albany, NY",
            email: "<EMAIL>",
            certifications: "Adobe Certified Expert in Advanced ColdFusion (2010)",
            summary: "Experienced technology leader and Full Stack ColdFusion Developer with over 20 years of experience in enterprise web systems, database optimization, application migration, and federal tax solutions. Leading Fusion Infotek's US operations with a focus on delivering innovative solutions.",
            skills: ["Team Leadership", "ColdFusion", "Python", "JavaScript", "HTML5", "CSS", "AngularJS", "Oracle", "SQL Server", "DevOps", "Git", "SVN", "Project Management", "Business Strategy"],
            experience: [
                "US Branch Head – Fusion Infotek (Present)",
                "Software Consultant – NYS IT Services (2023–Present)",
                "Consultant – PwC (2018–2023)",
                "Tech Lead – AT&T (2014–2018)",
                "ColdFusion Developer – AON, Sun Life, IBM, CSL (2005–2014)"
            ],
            social: {
                facebook: "https://www.facebook.com/shrawanxp",
                email: "mailto:<EMAIL>"
            }
        },
        prasiddha: {
            name: "Prasiddha Regmi",
            role: "Nepal Branch Head & Full Stack Developer",
            education: "MSc in Computer Science",
            location: "Banasthali, Kathmandu",
            email: "<EMAIL>",
            portfolio: "https://regmicode.com",
            summary: "Experienced Full Stack Developer and technology leader with expertise in modern web technologies, software architecture, and team management. Leading Fusion Infotek's Nepal operations while driving innovation and delivering cutting-edge solutions.",
            skills: ["Full Stack Development", "React", "Node.js", "JavaScript", "TypeScript", "Python", "PHP", "MySQL", "MongoDB", "Web Technologies", "Software Architecture", "Team Leadership", "Project Management", "Innovation"],
            experience: [
                "Nepal Branch Head & Full Stack Developer – Fusion Infotek (Present)",
                "Full Stack Developer – Various Projects",
                "Web Developer – Multiple Client Projects",
                "Software Development Consultant"
            ],
            social: {
                facebook: "https://www.facebook.com/prasiddha.regmi.355",
                linkedin: "https://www.linkedin.com/in/prasiddha-regmi-693763211/",
                github: "https://github.com/Prasiddha9999",
                email: "mailto:<EMAIL>"
            }
        },
        rahul: {
            name: "Rahul Sharma",
            role: "Senior Oracle PL/SQL Developer",
            education: "MCA (2005)",
            location: "Albany, NY",
            email: "<EMAIL>",
            phone: "(*************",
            summary: "Senior Database Developer with 18+ years' experience in RDBMS design, PL/SQL programming, data migration, performance optimization, and supporting enterprise-scale applications.",
            skills: ["Oracle", "PL/SQL", "SQL Server", "MySQL", "ColdFusion", ".NET", "Python", "Oracle Cloud Infrastructure"],
            experience: [
                "Software Consultant – NYS IT Services (2023–Present)",
                "Oracle PL/SQL Developer – PwC (2018–2023)",
                "PL/SQL Developer – AT&T (2014–2018)",
                "Developer – AON Consulting, Sun Life Financial, IBM India, CSL India"
            ],
            social: {
                email: "mailto:<EMAIL>",
                phone: "tel:+13143266770"
            }
        },
        vijay: {
            name: "Vijay K Singh",
            role: "Lead Software Engineer / Technical Project Manager",
            education: "MCA – Sikkim Manipal University (2006)",
            location: "St. Louis, MO",
            email: "<EMAIL>",
            phone: "(*************",
            summary: "Lead Software Engineer/Technical Project Manager with 17+ years of experience in full-stack enterprise web development. Expertise in Angular, TypeScript, ColdFusion, Spring Boot, .NET.",
            skills: ["Angular", "TypeScript", "ColdFusion", "Spring Boot", "ASP.NET", "Jenkins", "SQL Server", "Oracle", "MongoDB"],
            experience: [
                "Cognizant Technology Solutions (2013–Present)",
                "Tech Mahindra Ltd. (2010–2013)",
                "Wipro Technologies (2007–2010)",
                "Digital Domain India & Modulus System"
            ],
            social: {
                email: "mailto:<EMAIL>",
                phone: "tel:+***********"
            }
        },

        abin: {
            name: "Abin Regmi",
            role: "Expert Business Analyst / Product Owner",
            education: "MBA – Lincoln University, Oakland, CA",
            location: "Albany, NY",
            email: "<EMAIL>",
            phone: "(*************",
            linkedin: "https://www.linkedin.com/in/abin-regmi-405805244/",
            summary: "Experienced Business Analyst with 8+ years in public health and technology projects. Expert in requirements gathering, data analysis, Agile methodologies, and AWS-based solutions.",
            skills: ["AWS", "Python", "Tableau", "Grafana", "JIRA", "Confluence", "SQL", "Power BI", "Business Analysis"],
            certifications: ["ICP-APO – ICAgile Certified Product Owner", "Agile for Business Analysis", "Microsoft & LinkedIn Career Essentials"],
            experience: [
                "NYS Department of Health (2020–2024)",
                "NYS Office of Information Technology Services (2016–2020)"
            ],
            social: {
                email: "mailto:<EMAIL>",
                linkedin: "https://www.linkedin.com/in/abin-regmi-405805244/",
                phone: "tel:+***********"
            }
        }
    };
}

// Show team member portfolio
function showTeamPortfolio(memberKey) {
    // Handle legacy 'shrban' key - redirect to 'shrawan' since they're the same person
    if (memberKey === 'shrban') {
        memberKey = 'shrawan';
    }

    const member = window.teamData[memberKey];
    if (!member) return;

    // Special case for Prasiddha - redirect to portfolio website
    if (memberKey === 'prasiddha' && member.portfolio) {
        window.open(member.portfolio, '_blank');
        return;
    }

    // Create modal content
    const modalContent = createTeamModalContent(member);

    // Create and show modal
    const modal = document.createElement('div');
    modal.className = 'team-portfolio-modal';
    modal.innerHTML = modalContent;
    document.body.appendChild(modal);

    // Show modal with animation
    setTimeout(() => modal.classList.add('show'), 10);

    // Close modal functionality
    const closeBtn = modal.querySelector('.close-modal');
    const modalOverlay = modal.querySelector('.modal-overlay');

    const closeModal = () => {
        modal.classList.remove('show');
        setTimeout(() => document.body.removeChild(modal), 300);
    };

    closeBtn.addEventListener('click', closeModal);
    modalOverlay.addEventListener('click', closeModal);

    // ESC key to close
    const escHandler = (e) => {
        if (e.key === 'Escape') {
            closeModal();
            document.removeEventListener('keydown', escHandler);
        }
    };
    document.addEventListener('keydown', escHandler);
}

// Create team modal content
function createTeamModalContent(member) {
    const skillsHtml = member.skills ? member.skills.map(skill => `<span class="skill-tag">${skill}</span>`).join('') : '';
    const experienceHtml = member.experience ? member.experience.map(exp => `<li>${exp}</li>`).join('') : '';
    const certificationsHtml = member.certifications ? (Array.isArray(member.certifications) ? member.certifications.map(cert => `<li>${cert}</li>`).join('') : `<li>${member.certifications}</li>`) : '';

    let socialLinksHtml = '';
    if (member.social) {
        socialLinksHtml = Object.entries(member.social).map(([platform, url]) => {
            const icons = {
                email: 'fas fa-envelope',
                phone: 'fas fa-phone',
                linkedin: 'fab fa-linkedin',
                github: 'fab fa-github',
                facebook: 'fab fa-facebook'
            };
            return `<a href="${url}" class="social-link" target="_blank"><i class="${icons[platform] || 'fas fa-link'}"></i></a>`;
        }).join('');
    }

    return `
        <div class="modal-overlay"></div>
        <div class="modal-content">
            <button class="close-modal">&times;</button>
            <div class="team-modal-header">
                <h3>${member.name}</h3>
                <p class="modal-role">${member.role}</p>
                <div class="modal-location">
                    <i class="fas fa-map-marker-alt"></i> ${member.location}
                </div>
            </div>
            <div class="team-modal-body">
                <div class="modal-section">
                    <h4>About</h4>
                    <p>${member.summary}</p>
                </div>

                <div class="modal-section">
                    <h4>Education</h4>
                    <p><i class="fas fa-graduation-cap"></i> ${member.education}</p>
                    ${member.certifications ? `
                        <h5>Certifications</h5>
                        <ul class="certifications-list">${certificationsHtml}</ul>
                    ` : ''}
                </div>

                ${member.skills ? `
                    <div class="modal-section">
                        <h4>Technical Skills</h4>
                        <div class="skills-container">${skillsHtml}</div>
                    </div>
                ` : ''}

                ${member.experience ? `
                    <div class="modal-section">
                        <h4>Professional Experience</h4>
                        <ul class="experience-list">${experienceHtml}</ul>
                    </div>
                ` : ''}

                <div class="modal-section">
                    <h4>Contact</h4>
                    <div class="contact-info">
                        <p><i class="fas fa-envelope"></i> ${member.email}</p>
                        ${member.phone ? `<p><i class="fas fa-phone"></i> ${member.phone}</p>` : ''}
                        <div class="social-links">${socialLinksHtml}</div>
                    </div>
                </div>
            </div>
        </div>
    `;
}
